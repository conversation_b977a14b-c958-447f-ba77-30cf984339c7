<?php
require_once 'config.php';

$searchQuery = isset($_GET['q']) ? trim($_GET['q']) : '';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$perPage = 12;
$offset = ($page - 1) * $perPage;

function getDBConnection() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("数据库连接失败: " . $conn->connect_error);
    }
    return $conn;
}

// 获取数据库连接
$conn = getDBConnection(); // 或者直接使用方案2的代码

// 构建搜索SQL
if (!empty($searchQuery)) {
    $sql = "SELECT *, MATCH(description) AGAINST(? IN BOOLEAN MODE) AS score 
            FROM images 
            WHERE MATCH(description) AGAINST(? IN BOOLEAN MODE)
            ORDER BY score DESC, upload_time DESC 
            LIMIT ?, ?";
    $stmt = $conn->prepare($sql);
    $searchParam = "$searchQuery*";
    $stmt->bind_param("ssii", $searchParam, $searchParam, $offset, $perPage);
} else {
    $sql = "SELECT * FROM images ORDER BY upload_time DESC LIMIT ?, ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $offset, $perPage);
}

$stmt->execute();
$result = $stmt->get_result();
$images = $result->fetch_all(MYSQLI_ASSOC);

// 获取总记录数
if (!empty($searchQuery)) {
    $countSql = "SELECT COUNT(*) as total FROM images 
                 WHERE MATCH(description) AGAINST(? IN BOOLEAN MODE)";
    $countStmt = $conn->prepare($countSql);
    $countStmt->bind_param("s", $searchParam);
} else {
    $countSql = "SELECT COUNT(*) as total FROM images";
    $countStmt = $conn->prepare($countSql);
}

$countStmt->execute();
$totalResult = $countStmt->get_result()->fetch_assoc();
$totalItems = $totalResult['total'];
$totalPages = ceil($totalItems / $perPage);

$conn->close();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IDC搜索结果</title>
    <link href="bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>IDC搜索结果</h1>
        <?php if (!empty($searchQuery)): ?>
            <p class="text-muted">搜索关键词: "<?= htmlspecialchars($searchQuery) ?>"</p>
        <?php endif; ?>
        
        <div class="row row-cols-1 row-cols-md-3 g-4">
            <?php if (empty($images)): ?>
        <div class="col-12">
          <div class="alert alert-info text-center">没有找到匹配的结果</div>
        </div>
            <?php else: ?>
                <?php foreach ($images as $image): ?>
                    <div class="col">
                        <div class="card h-100">
                            <img src="<?= htmlspecialchars($image['filepath']) ?>" class="card-img-top" alt="<?= htmlspecialchars($image['description']) ?>">
                            <div class="card-body">
                                <p class="card-text"><?= htmlspecialchars($image['description']) ?></p>
                                <p class="text-muted"><small>上传时间: <?= $image['upload_time'] ?></small></p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <?php if ($totalPages > 1): ?>
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?q=<?= urlencode($searchQuery) ?>&page=<?= $page-1 ?>">上一页</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                            <a class="page-link" href="?q=<?= urlencode($searchQuery) ?>&page=<?= $i ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?q=<?= urlencode($searchQuery) ?>&page=<?= $page+1 ?>">下一页</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</body>
</html>
