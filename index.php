<?php require_once 'config.php'; ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传系统</title>
    <!-- Bootstrap CSS -->
    <link href="./bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding-top: 20px; }
        .upload-container { max-width: 800px; margin: 0 auto; }
        .preview-image { max-width: 100%; height: auto; margin-top: 15px; }
    </style>
</head>
<body>
    <div class="container upload-container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h1 class="h4 mb-0">IDC管理系统</h1>
            </div>
            <div class="card-body">
                <h2 class="card-title">搜索IDC</h2>
                <form action="search.php" method="GET" class="row g-3">
                    <div class="col-md-8">
                        <input type="text" name="q" class="form-control" placeholder="输入关键词">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">搜索</button>
                    </div>
                </form>
            </div>

            <div class="card-body">
                <?php if (isset($_SESSION['upload_error'])): ?>
                    <div class="alert alert-danger"><?= $_SESSION['upload_error'] ?></div>
                    <?php unset($_SESSION['upload_error']); ?>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['upload_success'])): ?>
                    <div class="alert alert-success"><?= $_SESSION['upload_success'] ?></div>
                    <?php unset($_SESSION['upload_success']); ?>
                <?php endif; ?>
                <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">注册IDC</button>
                <button class="btn btn-primary" onclick="window.location.href='idc.html'">查看最新图片</button>
                <a href="admin.php" class="btn btn-outline-secondary">管理员入口</a>
                <div class="collapse" id="collapseExample">
<h2>注册IDC</h2>
<form action="upload.php" method="POST" enctype="multipart/form-data">
    <div class="mb-3">
        <textarea class="form-control" name="description" placeholder="输入IDC名称" required></textarea>
    </div>
    
    <div class="mb-3"> <!-- 新增联系方式输入框 -->
        <input type="text" class="form-control" name="uploader" placeholder="输入联系方式" required>
    </div>
    
    <div class="mb-3">
        <label for="image" class="form-label">选择Logo (JPEG/PNG/GIF, 最大5MB)</label>
        <input class="form-control" type="file" name="image" accept="image/*" required>
    </div>
    
    <div class="d-grid gap-2">
        <button type="submit" class="btn btn-primary">上传图片</button>
    </div>
</form>
</div>
        </div>
    </div>
</div>
<script src="bootstrap.bundle.min.js"></script>
</body>
</html>
