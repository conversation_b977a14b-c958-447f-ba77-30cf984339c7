<?php
require_once 'config.php';

// 检查登录状态
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: admin.php');
    exit;
}

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("数据库连接失败: " . $conn->connect_error);
}

$result = $conn->query("SELECT * FROM images ORDER BY upload_time DESC");
$images = $result->fetch_all(MYSQLI_ASSOC);
$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理</title>
    <!-- Bootstrap CSS -->
    <link href="bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding-top: 20px; }
        .image-card { transition: transform 0.3s; }
        .image-card:hover { transform: translateY(-5px); }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">IDC管理</h1>
            <a href="logout.php" class="btn btn-outline-danger">退出登录</a>
        </div>
        
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            <?php foreach ($images as $image): ?>
            <div class="col">
                <div class="card h-100 image-card">
                    <img src="<?= htmlspecialchars($image['filepath']) ?>" class="card-img-top" alt="<?= htmlspecialchars($image['description']) ?>">
                    <div class="card-body">
                        <p class="card-text"><?= htmlspecialchars($image['description']) ?></p>
                        <p class="card-text"><small class="text-muted">联系方式: <?= htmlspecialchars($image['uploader']) ?></small></p>
                        <p class="card-text"><small class="text-muted">上传时间: <?= $image['upload_time'] ?></small></p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <form action="delete.php" method="POST" onsubmit="return confirm('确定要删除这张图片吗？');">
                            <input type="hidden" name="id" value="<?= $image['id'] ?>">
                            <input type="hidden" name="filepath" value="<?= $image['filepath'] ?>">
                            <button type="submit" class="btn btn-sm btn-danger w-100">删除</button>
                        </form>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="bootstrap.bundle.min.js"></script>
</body>
</html>
