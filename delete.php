<?php
require_once 'config.php';

// 检查登录状态
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: admin.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: dashboard.php');
    exit;
}

$id = intval($_POST['id']);
$filepath = $_POST['filepath'];

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("数据库连接失败: " . $conn->connect_error);
}

// 从数据库删除记录
$stmt = $conn->prepare("DELETE FROM images WHERE id = ?");
$stmt->bind_param("i", $id);
$deleted = $stmt->execute();
$stmt->close();

// 如果数据库删除成功，删除文件
if ($deleted && file_exists($filepath)) {
    unlink($filepath);
}

$conn->close();
header('Location: dashboard.php');
?>