CREATE TABLE images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VA<PERSON>HAR(255) NOT NULL,
    filepath VARCHAR(255) NOT NULL,
    description TEXT,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE images ADD FULLTEXT INDEX ft_search (description, tags);
ALTER TABLE images ADD COLUMN tags VARCHAR(255) AFTER description;
ALTER TABLE images ADD COLUMN uploader VARCHAR(50) AFTER tags;
