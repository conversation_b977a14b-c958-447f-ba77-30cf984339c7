<?php
require_once 'config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['upload_error'] = '无效的请求方法';
    header('Location: index.php');
    exit;
}

// 验证文件上传
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    $_SESSION['upload_error'] = '文件上传失败';
    header('Location: index.php');
    exit;
}

$file = $_FILES['image'];
$description = trim($_POST['description']);
$uploader = trim($_POST['uploader']); // 新增这行


// 验证文件类型和大小
if (!in_array($file['type'], ALLOWED_TYPES)) {
    $_SESSION['upload_error'] = '只允许上传JPEG、PNG或GIF图片';
    header('Location: index.php');
    exit;
}

if ($file['size'] > MAX_FILE_SIZE) {
    $_SESSION['upload_error'] = '文件大小不能超过5MB';
    header('Location: index.php');
    exit;
}

// 验证图片有效性
if (!getimagesize($file['tmp_name'])) {
    $_SESSION['upload_error'] = '无效的图片文件';
    header('Location: index.php');
    exit;
}

// 创建上传目录（如果不存在）
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}

// 生成唯一文件名
$fileExt = pathinfo($file['name'], PATHINFO_EXTENSION);
$filename = uniqid() . '.' . $fileExt;
$filepath = UPLOAD_DIR . $filename;

// 移动上传的文件
if (!move_uploaded_file($file['tmp_name'], $filepath)) {
    $_SESSION['upload_error'] = '文件保存失败';
    header('Location: index.php');
    exit;
}

// 将信息存入数据库
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    unlink($filepath);
    $_SESSION['upload_error'] = '数据库连接失败';
    header('Location: index.php');
    exit;
}

$stmt = $conn->prepare("INSERT INTO images (filename, filepath, description, uploader) VALUES (?, ?, ?, ?)");
$stmt->bind_param("ssss", $file['name'], $filepath, $description, $uploader); // 修改为4个参数

if ($stmt->execute()) {
    $_SESSION['upload_success'] = '图片上传成功';
} else {
    // 如果数据库插入失败，删除已上传的文件
    unlink($filepath);
    $_SESSION['upload_error'] = '数据库错误: ' . $conn->error;
}

$stmt->close();
$conn->close();
header('Location: index.php');
?>